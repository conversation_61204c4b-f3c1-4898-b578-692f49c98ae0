<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XAUUSD交易日志与分析系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .app-container {
            display: flex;
            height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background-color: #2c3e50;
            color: white;
            padding: 20px 0;
        }
        
        .logo {
            text-align: center;
            padding: 20px;
            border-bottom: 1px solid #34495e;
            margin-bottom: 20px;
        }
        
        .logo h1 {
            font-size: 18px;
            font-weight: 600;
        }
        
        .nav-menu {
            list-style: none;
        }
        
        .nav-item {
            margin: 5px 0;
        }
        
        .nav-link {
            display: block;
            padding: 15px 25px;
            color: #ecf0f1;
            text-decoration: none;
            transition: background-color 0.3s;
        }
        
        .nav-link:hover,
        .nav-link.active {
            background-color: #3498db;
        }
        
        .main-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .page-header {
            margin-bottom: 30px;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .welcome-message {
            text-align: center;
            margin-top: 100px;
        }
        
        .welcome-message h2 {
            color: #3498db;
            margin-bottom: 20px;
        }
        
        .welcome-message p {
            color: #7f8c8d;
            font-size: 16px;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
            color: #7f8c8d;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 侧边栏导航 -->
        <nav class="sidebar">
            <div class="logo">
                <h1>XAUUSD交易日志</h1>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#dashboard" class="nav-link active" data-page="dashboard">
                        📊 仪表盘
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#trades" class="nav-link" data-page="trades">
                        📈 交易列表
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#capital" class="nav-link" data-page="capital">
                        💰 资金与日志
                    </a>
                </li>
            </ul>
        </nav>
        
        <!-- 主内容区域 -->
        <main class="main-content">
            <div class="page-header">
                <h1 class="page-title" id="page-title">仪表盘</h1>
            </div>
            
            <div id="page-content">
                <div class="welcome-message">
                    <h2>欢迎使用XAUUSD交易日志系统</h2>
                    <p>系统正在初始化中，请稍候...</p>
                </div>
            </div>
        </main>
    </div>
    
    <script>
        // 简单的页面路由系统
        const navLinks = document.querySelectorAll('.nav-link');
        const pageTitle = document.getElementById('page-title');
        const pageContent = document.getElementById('page-content');
        
        // 页面配置
        const pages = {
            dashboard: {
                title: '仪表盘',
                content: `
                    <div class="welcome-message">
                        <h2>仪表盘</h2>
                        <p>这里将显示交易分析图表和关键指标</p>
                        <p>正在开发中...</p>
                    </div>
                `
            },
            trades: {
                title: '交易列表',
                content: `
                    <div class="welcome-message">
                        <h2>交易列表</h2>
                        <p>这里将显示所有交易记录和录入功能</p>
                        <p>正在开发中...</p>
                    </div>
                `
            },
            capital: {
                title: '资金与日志',
                content: `
                    <div class="welcome-message">
                        <h2>资金与日志</h2>
                        <p>这里将显示资金变动记录和每日复盘日志</p>
                        <p>正在开发中...</p>
                    </div>
                `
            }
        };
        
        // 导航点击事件
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                
                // 移除所有active类
                navLinks.forEach(l => l.classList.remove('active'));
                
                // 添加active类到当前链接
                link.classList.add('active');
                
                // 获取页面名称
                const pageName = link.getAttribute('data-page');
                
                // 更新页面内容
                if (pages[pageName]) {
                    pageTitle.textContent = pages[pageName].title;
                    pageContent.innerHTML = pages[pageName].content;
                }
            });
        });
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('XAUUSD交易日志系统已启动');
            
            // 模拟初始化延迟
            setTimeout(() => {
                pageContent.innerHTML = pages.dashboard.content;
            }, 1000);
        });
    </script>
</body>
</html>
