// 主应用类
class TradingJournalApp {
    constructor() {
        this.currentPage = 'dashboard';
        this.init();
    }

    init() {
        this.setupNavigation();
        this.loadPage('dashboard');
        console.log('XAUUSD交易日志系统已启动');
    }

    // 设置导航
    setupNavigation() {
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                
                // 移除所有active类
                navLinks.forEach(l => l.classList.remove('active'));
                
                // 添加active类到当前链接
                link.classList.add('active');
                
                // 获取页面名称并加载
                const pageName = link.getAttribute('data-page');
                this.loadPage(pageName);
            });
        });
    }

    // 加载页面
    loadPage(pageName) {
        this.currentPage = pageName;
        const pageTitle = document.getElementById('page-title');
        const pageContent = document.getElementById('page-content');

        // 销毁现有图表
        window.chartManager.destroyAll();

        switch (pageName) {
            case 'dashboard':
                pageTitle.textContent = '仪表盘';
                this.loadDashboard(pageContent);
                break;
            case 'trades':
                pageTitle.textContent = '交易列表';
                this.loadTrades(pageContent);
                break;
            case 'capital':
                pageTitle.textContent = '资金与日志';
                this.loadCapital(pageContent);
                break;
            default:
                pageContent.innerHTML = '<div class="loading"><h2>页面未找到</h2></div>';
        }
    }

    // 加载仪表盘
    loadDashboard(container) {
        const stats = window.db.getTradeStats();
        
        container.innerHTML = `
            <div class="grid grid-4" style="margin-bottom: 30px;">
                <div class="kpi-card">
                    <div class="kpi-value">$${stats.totalPnL.toFixed(2)}</div>
                    <div class="kpi-label">总净利润</div>
                </div>
                <div class="kpi-card">
                    <div class="kpi-value">${stats.totalTrades}</div>
                    <div class="kpi-label">总交易笔数</div>
                </div>
                <div class="kpi-card">
                    <div class="kpi-value">${stats.winRate.toFixed(1)}%</div>
                    <div class="kpi-label">胜率</div>
                </div>
                <div class="kpi-card">
                    <div class="kpi-value">$${stats.currentBalance.toFixed(2)}</div>
                    <div class="kpi-label">当前余额</div>
                </div>
            </div>
            
            <div class="grid grid-2" style="margin-bottom: 30px;">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">资金曲线</h3>
                    </div>
                    <div style="height: 300px;">
                        <canvas id="equity-chart"></canvas>
                    </div>
                </div>
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">做多/做空分布</h3>
                    </div>
                    <div style="height: 300px;">
                        <canvas id="pnl-distribution-chart"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="grid grid-2">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">月度盈亏</h3>
                    </div>
                    <div style="height: 300px;">
                        <canvas id="monthly-pnl-chart"></canvas>
                    </div>
                </div>
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">盈亏日历</h3>
                    </div>
                    <div id="pnl-calendar"></div>
                </div>
            </div>
            
            <div class="card" style="margin-top: 20px;">
                <div class="card-header">
                    <h3 class="card-title">详细统计</h3>
                </div>
                <div class="grid grid-3">
                    <div>
                        <strong>平均盈利:</strong> $${stats.avgWin.toFixed(2)}<br>
                        <strong>平均亏损:</strong> $${stats.avgLoss.toFixed(2)}<br>
                        <strong>盈亏比:</strong> ${stats.avgLoss > 0 ? (stats.avgWin / stats.avgLoss).toFixed(2) : 'N/A'}
                    </div>
                    <div>
                        <strong>做多胜率:</strong> ${stats.longWinRate.toFixed(1)}%<br>
                        <strong>做空胜率:</strong> ${stats.shortWinRate.toFixed(1)}%<br>
                        <strong>盈利因子:</strong> ${stats.profitFactor.toFixed(2)}
                    </div>
                    <div>
                        <strong>最大回撤:</strong> ${stats.maxDrawdown.toFixed(2)}%<br>
                        <strong>数据更新:</strong> ${new Date().toLocaleString()}
                    </div>
                </div>
            </div>
        `;

        // 延迟创建图表，确保DOM已渲染
        setTimeout(() => {
            window.chartManager.createEquityCurve('equity-chart', stats);
            window.chartManager.createPnLDistribution('pnl-distribution-chart', stats);
            window.chartManager.createMonthlyPnL('monthly-pnl-chart');
            window.chartManager.createPnLCalendar('pnl-calendar');
        }, 100);
    }

    // 加载交易列表页面
    loadTrades(container) {
        const trades = window.db.getTrades().sort((a, b) => new Date(b.trade_date) - new Date(a.trade_date));
        
        container.innerHTML = `
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">添加新交易</h3>
                </div>
                <form id="trade-form">
                    <div class="grid grid-4">
                        <div class="form-group">
                            <label class="form-label">交易日期</label>
                            <input type="date" id="trade-date" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">交易类型</label>
                            <select id="trade-type" class="form-select" required>
                                <option value="">请选择</option>
                                <option value="long">做多 (Long)</option>
                                <option value="short">做空 (Short)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">盈亏金额 (USD)</label>
                            <input type="number" id="trade-pnl" class="form-input" step="0.01" placeholder="正数为盈利，负数为亏损" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">操作</label>
                            <button type="submit" class="btn btn-primary" style="width: 100%; margin-top: 5px;">添加交易</button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">交易备注</label>
                        <textarea id="trade-notes" class="form-textarea" placeholder="记录交易理由、图表形态等信息..."></textarea>
                    </div>
                </form>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">交易历史 (${trades.length} 笔)</h3>
                </div>
                <div style="overflow-x: auto;">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>日期</th>
                                <th>类型</th>
                                <th>盈亏</th>
                                <th>备注</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="trades-table-body">
                            ${trades.map(trade => `
                                <tr>
                                    <td>${trade.trade_date}</td>
                                    <td>
                                        <span style="color: ${trade.trade_type === 'long' ? '#27ae60' : '#e74c3c'}">
                                            ${trade.trade_type === 'long' ? '做多' : '做空'}
                                        </span>
                                    </td>
                                    <td style="color: ${trade.pnl >= 0 ? '#27ae60' : '#e74c3c'}">
                                        ${trade.pnl >= 0 ? '+' : ''}$${trade.pnl.toFixed(2)}
                                    </td>
                                    <td>${trade.notes || '-'}</td>
                                    <td>
                                        <button class="btn btn-danger" onclick="app.deleteTrade('${trade.id}')">删除</button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                    ${trades.length === 0 ? '<p style="text-align: center; color: #7f8c8d; padding: 20px;">暂无交易记录</p>' : ''}
                </div>
            </div>
        `;

        // 设置默认日期为今天
        document.getElementById('trade-date').value = new Date().toISOString().split('T')[0];

        // 绑定表单提交事件
        document.getElementById('trade-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.addTrade();
        });
    }

    // 添加交易
    addTrade() {
        const tradeData = {
            trade_date: document.getElementById('trade-date').value,
            trade_type: document.getElementById('trade-type').value,
            pnl: document.getElementById('trade-pnl').value,
            notes: document.getElementById('trade-notes').value
        };

        try {
            window.db.addTrade(tradeData);
            
            // 重新加载交易列表
            this.loadPage('trades');
            
            // 显示成功消息
            this.showMessage('交易记录添加成功！', 'success');
        } catch (error) {
            this.showMessage('添加交易记录失败：' + error.message, 'error');
        }
    }

    // 删除交易
    deleteTrade(tradeId) {
        if (confirm('确定要删除这笔交易记录吗？')) {
            try {
                window.db.deleteTrade(tradeId);
                this.loadPage('trades');
                this.showMessage('交易记录删除成功！', 'success');
            } catch (error) {
                this.showMessage('删除交易记录失败：' + error.message, 'error');
            }
        }
    }

    // 加载资金与日志页面
    loadCapital(container) {
        const movements = window.db.getCapitalMovements().sort((a, b) => new Date(b.movement_date) - new Date(a.movement_date));
        const journals = window.db.getJournalEntries().sort((a, b) => new Date(b.entry_date) - new Date(a.entry_date));

        container.innerHTML = `
            <div class="grid grid-2" style="margin-bottom: 30px;">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">资金变动</h3>
                    </div>
                    <form id="capital-form">
                        <div class="grid grid-3">
                            <div class="form-group">
                                <label class="form-label">日期</label>
                                <input type="date" id="movement-date" class="form-input" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">类型</label>
                                <select id="movement-type" class="form-select" required>
                                    <option value="">请选择</option>
                                    <option value="deposit">入金</option>
                                    <option value="withdrawal">出金</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">金额 (USD)</label>
                                <input type="number" id="movement-amount" class="form-input" step="0.01" min="0" required>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">添加记录</button>
                    </form>

                    <div style="margin-top: 20px;">
                        <h4>历史记录</h4>
                        <div style="overflow-x: auto;">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>日期</th>
                                        <th>类型</th>
                                        <th>金额</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${movements.map(movement => `
                                        <tr>
                                            <td>${movement.movement_date}</td>
                                            <td>
                                                <span style="color: ${movement.movement_type === 'deposit' ? '#27ae60' : '#e74c3c'}">
                                                    ${movement.movement_type === 'deposit' ? '入金' : '出金'}
                                                </span>
                                            </td>
                                            <td style="color: ${movement.movement_type === 'deposit' ? '#27ae60' : '#e74c3c'}">
                                                ${movement.movement_type === 'deposit' ? '+' : '-'}$${movement.amount.toFixed(2)}
                                            </td>
                                            <td>
                                                <button class="btn btn-danger" onclick="app.deleteCapitalMovement('${movement.id}')">删除</button>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                            ${movements.length === 0 ? '<p style="text-align: center; color: #7f8c8d; padding: 20px;">暂无资金变动记录</p>' : ''}
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">每日复盘日志</h3>
                    </div>
                    <form id="journal-form">
                        <div class="form-group">
                            <label class="form-label">日期</label>
                            <input type="date" id="journal-date" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">复盘内容</label>
                            <textarea id="journal-content" class="form-textarea" rows="8" placeholder="记录今日的交易心得、市场观察、策略调整等..."></textarea>
                        </div>
                        <button type="submit" class="btn btn-success">保存日志</button>
                        <button type="button" class="btn btn-primary" onclick="app.loadJournalByDate()">加载当日日志</button>
                    </form>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">历史日志 (${journals.length} 条)</h3>
                </div>
                <div class="grid grid-2">
                    ${journals.map(journal => `
                        <div class="card" style="margin-bottom: 15px;">
                            <div class="card-header">
                                <h4 style="margin: 0; font-size: 16px;">${journal.entry_date}</h4>
                            </div>
                            <div style="white-space: pre-wrap; line-height: 1.6;">${journal.content}</div>
                        </div>
                    `).join('')}
                </div>
                ${journals.length === 0 ? '<p style="text-align: center; color: #7f8c8d; padding: 20px;">暂无日志记录</p>' : ''}
            </div>
        `;

        // 设置默认日期为今天
        document.getElementById('movement-date').value = new Date().toISOString().split('T')[0];
        document.getElementById('journal-date').value = new Date().toISOString().split('T')[0];

        // 绑定表单提交事件
        document.getElementById('capital-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.addCapitalMovement();
        });

        document.getElementById('journal-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveJournal();
        });
    }

    // 添加资金变动
    addCapitalMovement() {
        const movementData = {
            movement_date: document.getElementById('movement-date').value,
            movement_type: document.getElementById('movement-type').value,
            amount: document.getElementById('movement-amount').value
        };

        try {
            window.db.addCapitalMovement(movementData);
            this.loadPage('capital');
            this.showMessage('资金变动记录添加成功！', 'success');
        } catch (error) {
            this.showMessage('添加资金变动记录失败：' + error.message, 'error');
        }
    }

    // 删除资金变动
    deleteCapitalMovement(movementId) {
        if (confirm('确定要删除这条资金变动记录吗？')) {
            try {
                window.db.deleteCapitalMovement(movementId);
                this.loadPage('capital');
                this.showMessage('资金变动记录删除成功！', 'success');
            } catch (error) {
                this.showMessage('删除资金变动记录失败：' + error.message, 'error');
            }
        }
    }

    // 保存日志
    saveJournal() {
        const journalData = {
            entry_date: document.getElementById('journal-date').value,
            content: document.getElementById('journal-content').value
        };

        if (!journalData.content.trim()) {
            this.showMessage('请输入日志内容', 'error');
            return;
        }

        try {
            window.db.addJournalEntry(journalData);
            this.loadPage('capital');
            this.showMessage('日志保存成功！', 'success');
        } catch (error) {
            this.showMessage('保存日志失败：' + error.message, 'error');
        }
    }

    // 按日期加载日志
    loadJournalByDate() {
        const date = document.getElementById('journal-date').value;
        const journal = window.db.getJournalByDate(date);

        if (journal) {
            document.getElementById('journal-content').value = journal.content;
            this.showMessage('已加载该日期的日志', 'info');
        } else {
            document.getElementById('journal-content').value = '';
            this.showMessage('该日期暂无日志记录', 'info');
        }
    }

    // 显示消息
    showMessage(message, type = 'info') {
        // 简单的消息提示实现
        const messageDiv = document.createElement('div');
        messageDiv.style.position = 'fixed';
        messageDiv.style.top = '20px';
        messageDiv.style.right = '20px';
        messageDiv.style.padding = '15px 20px';
        messageDiv.style.borderRadius = '5px';
        messageDiv.style.color = 'white';
        messageDiv.style.zIndex = '9999';
        messageDiv.style.fontSize = '14px';
        messageDiv.textContent = message;

        switch (type) {
            case 'success':
                messageDiv.style.backgroundColor = '#27ae60';
                break;
            case 'error':
                messageDiv.style.backgroundColor = '#e74c3c';
                break;
            default:
                messageDiv.style.backgroundColor = '#3498db';
        }

        document.body.appendChild(messageDiv);

        // 3秒后自动移除
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 3000);
    }
}

// 页面加载完成后启动应用
document.addEventListener('DOMContentLoaded', () => {
    window.app = new TradingJournalApp();
});
