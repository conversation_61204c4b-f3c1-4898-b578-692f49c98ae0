# XAUUSD交易日志与分析系统

一个简洁高效的本地化黄金交易日志与分析网页应用，帮助交易者记录、分析和优化交易策略。

## 🌟 功能特色

### 📊 仪表盘
- **实时KPI指标**: 总净利润、交易笔数、胜率、当前余额
- **资金曲线图**: 直观展示账户余额变化趋势
- **盈亏分布**: 做多/做空交易盈亏对比
- **月度统计**: 按月份统计盈亏情况
- **盈亏日历**: 热力图显示每日交易结果
- **详细统计**: 平均盈利、盈亏比、最大回撤等关键指标

### 📈 交易管理
- **快速录入**: 简单表单记录交易信息
- **交易历史**: 完整的交易记录列表
- **数据验证**: 确保数据准确性
- **灵活编辑**: 支持删除错误记录

### 💰 资金管理
- **资金变动**: 记录入金、出金操作
- **历史追踪**: 完整的资金流水记录
- **自动计算**: 实时更新账户余额

### 📝 复盘日志
- **每日记录**: 按日期管理交易心得
- **快速查看**: 历史日志一目了然
- **内容丰富**: 支持详细的复盘分析

## 🚀 快速开始

### 系统要求
- 现代浏览器 (Chrome, Firefox, Safari, Edge)
- Python 3.x (用于本地服务器)

### 启动应用

1. **下载项目**
   ```bash
   # 如果使用git
   git clone <repository-url>
   cd xauusd-trading-journal
   
   # 或直接下载解压到本地文件夹
   ```

2. **启动本地服务器**
   ```bash
   # 方法1: 使用Python
   python3 -m http.server 8080
   
   # 方法2: 使用npm (如果已安装)
   npm start
   ```

3. **打开浏览器**
   访问: http://localhost:8080

### 首次使用

1. **添加初始资金**: 在"资金与日志"页面记录初始入金
2. **录入交易**: 在"交易列表"页面添加您的交易记录
3. **查看分析**: 在"仪表盘"查看统计分析结果
4. **记录心得**: 在"资金与日志"页面写下每日复盘

## 📱 使用指南

### 交易录入
- **日期**: 选择交易发生的日期
- **类型**: 选择做多(Long)或做空(Short)
- **盈亏**: 输入净盈亏金额，正数为盈利，负数为亏损
- **备注**: 记录交易理由、图表形态等信息

### 资金管理
- **入金**: 向交易账户添加资金
- **出金**: 从交易账户提取资金
- 系统会自动计算当前账户余额

### 数据分析
- **胜率**: 盈利交易占总交易的百分比
- **盈亏比**: 平均盈利与平均亏损的比值
- **盈利因子**: 总盈利与总亏损的比值
- **最大回撤**: 资金曲线的最大下跌幅度

## 💾 数据存储

- **本地存储**: 所有数据保存在浏览器的localStorage中
- **数据安全**: 数据完全存储在您的本地设备上
- **隐私保护**: 无需网络连接，保护交易隐私

### 数据备份
```javascript
// 在浏览器控制台中执行以下代码导出数据
const data = window.db.exportData();
console.log(JSON.stringify(data, null, 2));
// 复制输出的JSON数据保存为备份文件
```

### 数据恢复
```javascript
// 在浏览器控制台中执行以下代码导入数据
const backupData = {/* 粘贴备份的JSON数据 */};
window.db.importData(backupData);
location.reload(); // 刷新页面
```

## 🎨 界面特色

- **响应式设计**: 支持桌面和移动设备
- **直观导航**: 清晰的侧边栏导航
- **数据可视化**: 丰富的图表展示
- **现代UI**: 简洁美观的界面设计

## 🔧 技术架构

- **前端**: 原生HTML + CSS + JavaScript
- **图表**: Chart.js 4.4.0
- **存储**: localStorage (浏览器本地存储)
- **服务器**: Python HTTP Server (开发环境)

## 📈 统计指标说明

| 指标 | 说明 | 计算方式 |
|------|------|----------|
| 总净利润 | 所有交易的盈亏总和 | Σ(每笔交易盈亏) |
| 胜率 | 盈利交易的比例 | 盈利交易数 / 总交易数 × 100% |
| 平均盈利 | 盈利交易的平均金额 | 总盈利 / 盈利交易数 |
| 平均亏损 | 亏损交易的平均金额 | 总亏损 / 亏损交易数 |
| 盈亏比 | 平均盈利与平均亏损的比值 | 平均盈利 / 平均亏损 |
| 盈利因子 | 总盈利与总亏损的比值 | 总盈利 / |总亏损| |
| 最大回撤 | 资金曲线的最大下跌幅度 | (峰值 - 谷值) / 峰值 × 100% |

## 🤝 支持与反馈

如果您在使用过程中遇到问题或有改进建议，欢迎反馈！

## 📄 许可证

MIT License - 详见 LICENSE 文件

---

**免责声明**: 本工具仅用于交易记录和分析，不构成投资建议。交易有风险，投资需谨慎。
