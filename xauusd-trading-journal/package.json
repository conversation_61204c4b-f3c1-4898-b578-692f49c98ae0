{"name": "xauusd-trading-journal", "version": "1.0.0", "description": "本地XAUUSD交易日志与分析系统", "main": "src/main/main.js", "scripts": {"dev": "electron .", "build": "electron-builder", "start": "electron .", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["trading", "journal", "xau<PERSON>d", "analysis", "electron"], "author": "Trading Journal Developer", "license": "MIT", "devDependencies": {"electron": "^28.3.3", "electron-builder": "^24.13.3"}, "dependencies": {"sqlite3": "^5.1.7", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "build": {"appId": "com.tradingjournal.xauusd", "productName": "XAUUSD Trading Journal", "directories": {"output": "dist"}, "files": ["src/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.finance"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}